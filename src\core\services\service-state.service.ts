import { Injectable } from '@nestjs/common';
import { ServiceStatus, ServiceName } from '../hub.types';
import { HubEventsService } from './hub-events.service';

/**
 * Service dédié à la gestion des statuts des services externes
 * Responsabilité : Gestion centralisée des connexions et statuts des services (Gabin, Companion, etc.)
 */
@Injectable()
export class ServiceStateService {
    private serviceStates: Record<ServiceName, ServiceStatus> = {
        gabin: { connected: false },
        companion: { connected: false },
    };

    // Gestion des timeouts pour les pings
    private serviceTimeouts: Record<string, NodeJS.Timeout> = {};

    constructor(private readonly hubEventsService: HubEventsService) {}

    /**
     * Met à jour le statut d'un service
     */
    setServiceStatus(serviceName: ServiceName, status: Partial<ServiceStatus>): void {
        // Annuler le timeout si on reçoit une réponse
        if (status.connected === true && this.serviceTimeouts[serviceName]) {
            clearTimeout(this.serviceTimeouts[serviceName]);
            delete this.serviceTimeouts[serviceName];
        }

        const currentStatus = this.serviceStates[serviceName];
        const newStatus = { ...currentStatus, ...status, lastSeen: new Date() };

        if (currentStatus.connected !== newStatus.connected) {
            console.log(`[ServiceState] Service '${serviceName}' status: ${newStatus.connected ? 'CONNECTED' : 'DISCONNECTED'}`);
            this.serviceStates[serviceName] = newStatus;

            this.hubEventsService.emitEvent({
                type: 'service_status_changed',
                data: { serviceName, status: newStatus },
            });
        } else {
            this.serviceStates[serviceName] = newStatus;
        }
    }

    /**
     * Récupère le statut d'un service
     */
    getServiceStatus(serviceName: ServiceName): ServiceStatus {
        return { ...this.serviceStates[serviceName] };
    }

    /**
     * Vérifie si un service est connecté
     */
    isServiceConnected(serviceName: ServiceName): boolean {
        return this.serviceStates[serviceName].connected;
    }

    /**
     * Stocke le timeout d'un service
     */
    setServiceTimeout(serviceName: ServiceName, timeoutId: NodeJS.Timeout): void {
        // Annuler l'ancien timeout s'il existe
        if (this.serviceTimeouts[serviceName]) {
            clearTimeout(this.serviceTimeouts[serviceName]);
        }
        this.serviceTimeouts[serviceName] = timeoutId;
    }

    /**
     * Récupère tous les statuts des services
     */
    getAllServiceStates(): Record<ServiceName, ServiceStatus> {
        return {
            gabin: { ...this.serviceStates.gabin },
            companion: { ...this.serviceStates.companion },
        };
    }
}
