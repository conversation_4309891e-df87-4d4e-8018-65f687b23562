/**
 * Configuration principale de l'application
 */

/**
 * Configuration du serveur HTTP
 */
export interface HttpConfig {
    port: number;
}

/**
 * Configuration générale de l'application
 */
export interface AppConfig {
    http: HttpConfig;
    environment: 'development' | 'production' | 'test';
}

/**
 * Configuration par défaut de l'application
 */
export const appConfig: AppConfig = {
    http: {
        port: process.env.HTTP_PORT ? parseInt(process.env.HTTP_PORT, 10) : 3000,
    },
    environment: (process.env.NODE_ENV as AppConfig['environment']) || 'development',
};
