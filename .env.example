# Configuration exemple pour obs-video-hub
# Copiez ce fichier vers .env et modifiez les valeurs selon vos besoins

# === Configuration HTTP ===
HTTP_PORT=3000

# === Configuration Gabin ===
# Port d'écoute pour recevoir les feedbacks de Gabin
GABIN_LISTEN_PORT=33123
# Port d'envoi pour envoyer les commandes vers Gabin
GABIN_SEND_PORT=32123
# Adresse IP de Gabin
GABIN_HOST=127.0.0.1
# Intervalle de ping vers Gabin (ms)
GABIN_PING_INTERVAL=5000
# Timeout du ping Gabin (ms)
GABIN_PING_TIMEOUT=3000
# Délai avant re-registration après reconnexion (ms)
GABIN_RECONNECTION_DELAY=500

# === Configuration Companion ===
# Port d'écoute pour recevoir les actions de Companion
COMPANION_LISTEN_PORT=33223
# Adresse IP de Companion
COMPANION_HOST=127.0.0.1
# Port de Companion pour envoyer les feedbacks
COMPANION_PORT=33224
# Intervalle de vérification de connexion Gabin (ms)
COMPANION_CHECK_INTERVAL=6000
# Délai avant envoi des états initiaux (ms)
COMPANION_INITIAL_DELAY=1000

# === Variables d'environnement ===
NODE_ENV=development
