# 🎥 OBS Video Hub

**Hub de communication centralisé pour OBS Studio, Gabin et Companion**

OBS Video Hub est une application Node.js/NestJS qui fait le pont entre différents outils de production vidéo :
- **Gabin** : Système de caméras automatiques
- **Companion** : Contrôleur de surfaces de contrôle
- **OBS Studio** : Logiciel de streaming/enregistrement

## 🏗️ Architecture

L'application utilise une architecture modulaire centralisée :

```
┌─────────────┐    OSC     ┌─────────────┐    OSC     ┌─────────────┐
│   Gabin     │ ◄────────► │ Video Hub   │ ◄────────► │ Companion   │
│             │            │             │            │             │
│ • Autocam   │            │ • État      │            │ • Actions   │
│ • Micros    │            │ • Events    │            │ • Feedbacks │
│ • Feedbacks │            │ • Config    │            │ • Boutons   │
└─────────────┘            └─────────────┘            └─────────────┘
                                   │
                                   │ HTTP
                                   ▼
                           ┌─────────────┐
                           │   Status    │
                           │   API       │
                           └─────────────┘
```

## 🚀 Installation et Premier Démarrage

### 1. Installation des dépendances

```bash
npm install
```

### 2. Premier démarrage (génération des configurations)

```bash
npm run start
```

**⚠️ Important :** Au premier démarrage, l'application va :
1. Générer automatiquement les fichiers de configuration locaux
2. S'arrêter avec un message explicatif
3. Vous demander de personnaliser les configurations si nécessaire

### 3. Configuration personnalisée (optionnel)

Après le premier démarrage, vous pouvez personnaliser :

- **`src/config/local/gabin.local.ts`** : Configuration Gabin (micros, ports, timeouts)
- **`src/config/local/companion.local.ts`** : Configuration Companion (feedbacks, actions)

> 💡 **Conseil :** Décommentez seulement les sections que vous voulez modifier

### 4. Démarrage normal

```bash
# Mode développement
npm run start:dev

# Mode production
npm run start:prod
```

## 🔧 Configuration

### Architecture de Configuration Hybride

L'application utilise un système de configuration hybride innovant :

```
src/config/
├── app.config.ts                   # Config générale (HTTP_PORT, NODE_ENV)
├── defaults/
│   ├── gabin.default.ts            # ⚠️ NE PAS MODIFIER (versionnés)
│   └── companion.default.ts        # ⚠️ NE PAS MODIFIER (versionnés)
├── local/
│   ├── gabin.local.ts              # 🔧 MODIFIER ICI (ignorés par Git)
│   └── companion.local.ts          # 🔧 MODIFIER ICI (ignorés par Git)
└── index.ts                        # Merge automatique
```

### Variables d'environnement (.env)

Créez un fichier `.env` pour la configuration générale :

```bash
# Configuration générale
HTTP_PORT=3000
NODE_ENV=development
```

### Configuration Gabin (gabin.local.ts)

```typescript
export const gabinLocalConfig: Partial<GabinConfig> = {
    // Ajoutez vos micros
    registrations: [
        { type: 'autocam', path: '/autocam' },
        { type: 'micFeedback', path: '/mic/MIC1' },
        { type: 'micFeedback', path: '/mic/MIC2' },
        { type: 'micFeedback', path: '/mic/MIC3' }, // Nouveau micro
    ],
};
```

### Configuration Companion (companion.local.ts)

```typescript
export const companionLocalConfig: Partial<CompanionConfig> = {
    // Ajoutez vos feedbacks
    feedbacks: [
        { type: 'autocam', path: '/autocam' },
        { type: 'mic', path: '/mic/MIC1', micName: 'MIC1' },
        { type: 'mic', path: '/mic/MIC2', micName: 'MIC2' },
        { type: 'mic', path: '/mic/MIC3', micName: 'MIC3' }, // Nouveau feedback
    ],
};
```

## 🌐 API et Endpoints

### Status API

**GET** `http://localhost:3000/status`

Retourne l'état complet du système :

```json
{
  "autocam": true,
  "mics": {
    "MIC1": true,
    "MIC2": false,
    "MIC3": true
  },
  "services": {
    "gabin": {
      "connected": true,
      "lastSeen": "2025-06-20T13:26:04.374Z"
    },
    "companion": {
      "connected": false
    }
  }
}
```

## 🎛️ Communication OSC

### Ports par défaut

- **Gabin** :
  - Écoute : `33123` (feedbacks depuis Gabin)
  - Envoi : `32123` (commandes vers Gabin)
- **Companion** :
  - Écoute : `33223` (actions depuis Companion)
  - Envoi : `33224` (feedbacks vers Companion)
- **HTTP** : `3000` (API REST)

### Messages OSC supportés

#### Depuis Gabin → Hub
- `/autocam` : État de l'autocam (0/1)
- `/mic/MIC1` : État du micro MIC1 (0/1)
- `/gabin/is-ready` : Réponse au ping de connexion

#### Depuis Hub → Gabin
- `/autocam` : Commande autocam (0/1)
- `/mic/MIC1` : Commande micro (0/1)
- `/gabin/is-ready` : Ping de connexion

#### Depuis Companion → Hub
- `/action/autocam/toggle` : Toggle autocam
- `/action/autocam/on` : Activer autocam
- `/action/autocam/off` : Désactiver autocam
- `/action/mic/MIC1/toggle` : Toggle micro MIC1
- `/companion/ready` : Signal de démarrage Companion

#### Depuis Hub → Companion
- `/autocam` : Feedback autocam (0/1/2)
- `/mic/MIC1` : Feedback micro (0/1/2)

> **Note :** Valeur 2 = État indéfini (bouton gris quand Gabin déconnecté)

## 🔄 Fonctionnalités

### ✅ Gestion d'état centralisée
- État unifié pour autocam et micros
- Synchronisation automatique entre services
- Persistance des états pendant les reconnexions

### 🔄 Reconnexion automatique
- Détection de déconnexion Gabin via ping
- Re-registration automatique des feedbacks
- Gestion des états "déconnecté" vers Companion

### 🎛️ Feedbacks intelligents
- Feedbacks automatiques vers Companion
- États gris quand Gabin déconnecté
- Synchronisation au démarrage de Companion

### 🔧 Configuration flexible
- Auto-génération des fichiers de configuration
- Merge intelligent defaults + local
- Git-friendly (configurations locales ignorées)

## 🛠️ Développement

### Structure du projet

```
src/
├── core/                           # Module central (Hub)
│   ├── hub.service.ts             # Orchestration principale
│   ├── hub.controller.ts          # API REST
│   └── services/
│       ├── hub-events.service.ts  # Système d'événements
│       ├── device-state.service.ts # État des devices
│       └── service-state.service.ts # État des services
├── gabin/                         # Module Gabin
│   ├── gabin.service.ts           # Logique métier Gabin
│   ├── gabin-transport.service.ts # Communication OSC
│   └── gabin.types.ts             # Types TypeScript
├── companion/                     # Module Companion
│   ├── companion-transport.service.ts # Communication OSC
│   └── companion.types.ts         # Types TypeScript
└── config/                        # Configuration hybride
    ├── defaults/                  # Configs par défaut (versionnées)
    ├── local/                     # Configs locales (ignorées Git)
    └── config.generator.ts        # Auto-génération
```

### Tests

```bash
# Tests unitaires
npm run test

# Tests e2e
npm run test:e2e

# Couverture
npm run test:cov
```

### Build

```bash
# Build de production
npm run build

# Démarrage production
npm run start:prod
```

## 🐛 Dépannage

### Gabin ne se connecte pas
1. Vérifiez que Gabin est démarré
2. Vérifiez les ports dans `gabin.local.ts`
3. Consultez les logs : `[gabin] Starting Gabin connection monitoring...`

### Companion ne reçoit pas les feedbacks
1. Vérifiez la configuration dans `companion.local.ts`
2. Envoyez `/companion/ready` depuis Companion
3. Vérifiez les logs : `[Companion] Received ready signal`

### Configuration non prise en compte
1. Redémarrez l'application après modification des fichiers locaux
2. Vérifiez la syntaxe TypeScript des fichiers de config
3. Consultez les logs de merge au démarrage

## 📝 Changelog

### v2.0.0 - Configuration Hybride
- ✅ Architecture de configuration hybride
- ✅ Auto-génération des fichiers locaux
- ✅ Git-friendly (configs locales ignorées)
- ✅ Messages d'aide intégrés

### v1.0.0 - Version Initiale
- ✅ Hub centralisé Gabin ↔ Companion
- ✅ Communication OSC bidirectionnelle
- ✅ API REST de status
- ✅ Gestion des reconnexions

## 📄 License

MIT License - Voir le fichier [LICENSE](LICENSE) pour plus de détails.
