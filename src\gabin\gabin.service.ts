import { Injectable } from '@nestjs/common';

@Injectable()
export class GabinService {
    private autocamState: boolean | null = null;
    private micStates: Record<string, boolean> = {};
    private autocamCallbacks: Array<(state: boolean) => void> = [];
    private micCallbacks: Array<(name: string, state: boolean) => void> = [];

    setAutocam(value: boolean) {
        if (this.autocamState !== value) {
            this.autocamState = value;
            console.log(`[gabin] Autocam changed to: ${value}`);

            // Notifier tous les callbacks
            this.autocamCallbacks.forEach((callback) => {
                try {
                    callback(value);
                } catch (error) {
                    console.error('[gabin] Error in autocam callback:', error);
                }
            });
        }
    }

    setMic(name: string, value: boolean) {
        if (this.micStates[name] !== value) {
            this.micStates[name] = value;
            console.log(`[gabin] Mic '${name}' changed to: ${value}`);

            // Notifier tous les callbacks
            this.micCallbacks.forEach((callback) => {
                try {
                    callback(name, value);
                } catch (error) {
                    console.error('[gabin] Error in mic callback:', error);
                }
            });
        }
    }

    getStatus() {
        return {
            autocam: this.autocamState,
            mics: this.micStates,
        };
    }

    getAutocam(): boolean | null {
        return this.autocamState;
    }

    getMic(name: string): boolean | undefined {
        return this.micStates[name];
    }

    onAutocamChange(callback: (state: boolean) => void) {
        this.autocamCallbacks.push(callback);
    }

    onMicChange(callback: (name: string, state: boolean) => void) {
        this.micCallbacks.push(callback);
    }

    // === COMMANDES POUR CONTRÔLER GABIN ===
    private commandCallbacks: {
        autocam: Array<(value: boolean) => void>;
        mic: Array<(name: string, value: boolean) => void>;
        ping?: Array<() => void>;
    } = { autocam: [], mic: [] };

    /**
     * Envoie une commande pour changer l'autocam (appelé par Companion)
     */
    sendAutocamCommand(value: boolean) {
        console.log(`[gabin] Sending autocam command: ${value}`);

        // Notifier les callbacks de commande (le transport écoutera)
        this.commandCallbacks.autocam.forEach((callback) => {
            try {
                callback(value);
            } catch (error) {
                console.error('[gabin] Error in autocam command callback:', error);
            }
        });
    }

    /**
     * Envoie une commande pour changer un micro (appelé par Companion)
     */
    sendMicCommand(name: string, value: boolean) {
        console.log(`[gabin] Sending mic command for '${name}': ${value}`);

        // Notifier les callbacks de commande (le transport écoutera)
        this.commandCallbacks.mic.forEach((callback) => {
            try {
                callback(name, value);
            } catch (error) {
                console.error('[gabin] Error in mic command callback:', error);
            }
        });
    }

    /**
     * S'abonner aux commandes autocam
     */
    onAutocamCommand(callback: (value: boolean) => void) {
        this.commandCallbacks.autocam.push(callback);
    }

    /**
     * S'abonner aux commandes micro
     */
    onMicCommand(callback: (name: string, value: boolean) => void) {
        this.commandCallbacks.mic.push(callback);
    }

    // === GESTION SIMPLE DE L'ÉTAT DE CONNEXION GABIN ===
    private gabinConnected: boolean | null = null; // null = inconnu, true/false = état connu
    private currentPingTimeout: NodeJS.Timeout | null = null;

    /**
     * Met à jour l'état de connexion de Gabin (appelé quand on reçoit une réponse à /gabin/is-ready)
     */
    setGabinConnected(connected: boolean) {
        // Annuler le timeout si on reçoit une réponse
        if (this.currentPingTimeout) {
            clearTimeout(this.currentPingTimeout);
            this.currentPingTimeout = null;
        }

        if (this.gabinConnected !== connected) {
            console.log(`[gabin] Gabin connection state: ${connected ? 'CONNECTED' : 'DISCONNECTED'}`);
            this.gabinConnected = connected;
        }
    }

    /**
     * Retourne l'état de connexion de Gabin
     */
    isGabinConnected(): boolean | null {
        return this.gabinConnected;
    }

    /**
     * Stocke le timeout du ping actuel
     */
    setPingTimeout(timeoutId: NodeJS.Timeout) {
        // Annuler l'ancien timeout s'il existe
        if (this.currentPingTimeout) {
            clearTimeout(this.currentPingTimeout);
        }
        this.currentPingTimeout = timeoutId;
    }
}
