import { Injectable } from '@nestjs/common';
import { HubService } from '../core/hub.service';

@Injectable()
export class GabinService {
    // Callbacks pour la compatibilité avec l'ancienne interface
    private autocamCallbacks: Array<(state: boolean) => void> = [];
    private micCallbacks: Array<(name: string, state: boolean) => void> = [];

    constructor(private readonly hubService: HubService) {
        // S'abonner aux événements du Hub pour maintenir la compatibilité
        this.hubService.onEvent((event) => {
            if (event.type === 'autocam_changed' && event.data.value !== undefined) {
                this.autocamCallbacks.forEach((callback) => {
                    try {
                        callback(event.data.value!);
                    } catch (error) {
                        console.error('[gabin] Error in autocam callback:', error);
                    }
                });
            } else if (event.type === 'mic_changed' && event.data.name && event.data.value !== undefined) {
                this.micCallbacks.forEach((callback) => {
                    try {
                        callback(event.data.name!, event.data.value!);
                    } catch (error) {
                        console.error('[gabin] Error in mic callback:', error);
                    }
                });
            }
        });
    }

    setAutocam(value: boolean) {
        // Déléguer vers HubService - les callbacks seront appelés via les événements
        this.hubService.setAutocam(value);
    }

    setMic(name: string, value: boolean) {
        // Déléguer vers HubService - les callbacks seront appelés via les événements
        this.hubService.setMic(name, value);
    }

    getStatus() {
        // Déléguer vers HubService pour maintenir la compatibilité
        const hubStatus = this.hubService.getStatus();
        return {
            autocam: hubStatus.autocam,
            mics: hubStatus.mics,
        };
    }

    getAutocam(): boolean | null {
        return this.hubService.getAutocam();
    }

    getMic(name: string): boolean | undefined {
        return this.hubService.getMic(name);
    }

    onAutocamChange(callback: (state: boolean) => void) {
        this.autocamCallbacks.push(callback);
    }

    onMicChange(callback: (name: string, state: boolean) => void) {
        this.micCallbacks.push(callback);
    }

    // === COMMANDES POUR CONTRÔLER GABIN ===
    private commandCallbacks: {
        autocam: Array<(value: boolean) => void>;
        mic: Array<(name: string, value: boolean) => void>;
        ping?: Array<() => void>;
    } = { autocam: [], mic: [] };

    /**
     * Envoie une commande pour changer l'autocam (appelé par Companion)
     */
    sendAutocamCommand(value: boolean) {
        console.log(`[gabin] Sending autocam command: ${value}`);

        // Notifier les callbacks de commande (le transport écoutera)
        this.commandCallbacks.autocam.forEach((callback) => {
            try {
                callback(value);
            } catch (error) {
                console.error('[gabin] Error in autocam command callback:', error);
            }
        });
    }

    /**
     * Envoie une commande pour changer un micro (appelé par Companion)
     */
    sendMicCommand(name: string, value: boolean) {
        console.log(`[gabin] Sending mic command for '${name}': ${value}`);

        // Notifier les callbacks de commande (le transport écoutera)
        this.commandCallbacks.mic.forEach((callback) => {
            try {
                callback(name, value);
            } catch (error) {
                console.error('[gabin] Error in mic command callback:', error);
            }
        });
    }

    /**
     * S'abonner aux commandes autocam
     */
    onAutocamCommand(callback: (value: boolean) => void) {
        this.commandCallbacks.autocam.push(callback);
    }

    /**
     * S'abonner aux commandes micro
     */
    onMicCommand(callback: (name: string, value: boolean) => void) {
        this.commandCallbacks.mic.push(callback);
    }

    // === GESTION SIMPLE DE L'ÉTAT DE CONNEXION GABIN ===
    private currentPingTimeout: NodeJS.Timeout | null = null;

    /**
     * Met à jour l'état de connexion de Gabin (appelé quand on reçoit une réponse à /gabin/is-ready)
     */
    setGabinConnected(connected: boolean) {
        // Annuler le timeout si on reçoit une réponse
        if (this.currentPingTimeout) {
            clearTimeout(this.currentPingTimeout);
            this.currentPingTimeout = null;
        }

        // Déléguer vers HubService
        this.hubService.setServiceStatus('gabin', { connected });
    }

    /**
     * Retourne l'état de connexion de Gabin
     */
    isGabinConnected(): boolean {
        return this.hubService.isServiceConnected('gabin');
    }

    /**
     * Stocke le timeout du ping actuel
     */
    setPingTimeout(timeoutId: NodeJS.Timeout) {
        // Annuler l'ancien timeout s'il existe
        if (this.currentPingTimeout) {
            clearTimeout(this.currentPingTimeout);
        }
        this.currentPingTimeout = timeoutId;
    }
}
