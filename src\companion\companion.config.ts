/**
 * Configuration des targets Companion
 * Cette configuration définit où envoyer les feedbacks automatiquement.
 * Modifiez ces valeurs selon votre setup Companion.
 */

export interface CompanionFeedbackConfig {
    type: 'autocam' | 'mic';
    path: string;
    micName?: string;
}

export interface CompanionConfig {
    host: string;
    port: number;
    feedbacks: CompanionFeedbackConfig[];
}

export const companionConfig: CompanionConfig = {
    // Adresse et port de Companion
    host: '127.0.0.1',
    port: 33224,
    // Liste des feedbacks à envoyer automatiquement
    feedbacks: [
        // Autocam
        {
            type: 'autocam',
            path: '/autocam',
        },
        // Micros
        {
            type: 'mic',
            path: '/mic/MIC1',
            micName: 'MIC1',
        },
        {
            type: 'mic',
            path: '/mic/MIC2',
            micName: 'MIC2',
        },
    ],
};
