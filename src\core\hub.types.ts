/**
 * Interface pour le statut des services
 */
export interface ServiceStatus {
    connected: boolean;
    lastSeen?: Date;
}

/**
 * Interface pour les événements du Hub
 */
export interface HubEvent {
    type: 'autocam_changed' | 'mic_changed' | 'service_status_changed';
    data: {
        value?: boolean;
        name?: string;
        serviceName?: string;
        status?: ServiceStatus;
    };
}

/**
 * Interface pour l'état global du Hub
 */
export interface HubState {
    // États des fonctionnalités
    autocam: boolean | null;
    mics: Record<string, boolean>;

    // États des services
    services: {
        gabin: ServiceStatus;
        companion: ServiceStatus;
    };
}

/**
 * Types pour les noms de services
 */
export type ServiceName = 'gabin' | 'companion';

/**
 * Types pour les événements de changement d'état des appareils
 */
export type DeviceEventType = 'autocam_changed' | 'mic_changed';

/**
 * Types pour les événements de changement de statut des services
 */
export type ServiceEventType = 'service_status_changed';

/**
 * Callback pour les événements du Hub
 */
export type HubEventCallback = (event: HubEvent) => void;
