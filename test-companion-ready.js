// Script de test pour simuler le signal /companion/ready
const { Client, Message } = require('node-osc');

const client = new Client('127.0.0.1', 33223);
const msg = new Message('/companion/ready');

console.log('Sending /companion/ready signal...');
client.send(msg, (err) => {
    if (err) {
        console.error('Error:', err);
    } else {
        console.log('Signal sent successfully!');
    }
    client.close();
});
