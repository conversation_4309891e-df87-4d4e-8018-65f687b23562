import { CompanionConfig } from '../companion/companion.types';

/**
 * Configuration pour le module Companion
 */
export const companionConfig: CompanionConfig = {
    network: {
        listenPort: process.env.COMPANION_LISTEN_PORT ? parseInt(process.env.COMPANION_LISTEN_PORT, 10) : 33223,
        host: process.env.COMPANION_HOST || '127.0.0.1',
        port: process.env.COMPANION_PORT ? parseInt(process.env.COMPANION_PORT, 10) : 33224,
    },
    timing: {
        connectionCheckInterval: process.env.COMPANION_CHECK_INTERVAL ? parseInt(process.env.COMPANION_CHECK_INTERVAL, 10) : 6000,
        initialStateDelay: process.env.COMPANION_INITIAL_DELAY ? parseInt(process.env.COMPANION_INITIAL_DELAY, 10) : 1000,
    },
    feedbacks: [
        // Autocam
        {
            type: 'autocam',
            path: '/autocam',
        },
        // Micros
        {
            type: 'mic',
            path: '/mic/MIC1',
            micName: 'MIC1',
        },
        {
            type: 'mic',
            path: '/mic/MIC2',
            micName: 'MIC2',
        },
        // Ajouter d'autres micros ici selon vos besoins
    ],
};
