import { Modu<PERSON> } from '@nestjs/common';
import { HubService } from './hub.service';
import { HubController } from './hub.controller';
import { HubEventsService } from './services/hub-events.service';
import { DeviceStateService } from './services/device-state.service';
import { ServiceStateService } from './services/service-state.service';

@Module({
    providers: [
        HubEventsService,
        DeviceStateService,
        ServiceStateService,
        HubService,
    ],
    controllers: [HubController],
    exports: [HubService],
})
export class HubModule {}
