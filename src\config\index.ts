/**
 * Export centralisé de toutes les configurations
 * Merge automatique des configurations par défaut + locales
 */

import { ConfigGenerator } from './config.generator';
import { gabinDefaultConfig } from './defaults/gabin.default';
import { companionDefaultConfig } from './defaults/companion.default';

// Auto-génération des fichiers locaux au premier démarrage
ConfigGenerator.generateMissingLocalConfigs();

// Import dynamique des configurations locales avec fallback
let gabinLocalConfig: any = {};
let companionLocalConfig: any = {};

try {
    const gabinLocal = require('./local/gabin.local');
    gabinLocalConfig = gabinLocal.gabinLocalConfig || {};
} catch (error) {
    // Fichier local n'existe pas ou erreur d'import - utiliser config vide
    console.log('[Config] No local Gabin config found, using defaults only');
}

try {
    const companionLocal = require('./local/companion.local');
    companionLocalConfig = companionLocal.companionLocalConfig || {};
} catch (error) {
    // Fichier local n'existe pas ou erreur d'import - utiliser config vide
    console.log('[Config] No local Companion config found, using defaults only');
}

/**
 * Merge profond de deux objets de configuration
 */
function deepMerge<T>(defaults: T, overrides: Partial<T>): T {
    const result = { ...defaults };

    for (const key in overrides) {
        if (overrides[key] !== undefined) {
            if (typeof overrides[key] === 'object' && !Array.isArray(overrides[key]) && overrides[key] !== null) {
                result[key] = deepMerge(result[key] as any, overrides[key] as any);
            } else {
                result[key] = overrides[key] as any;
            }
        }
    }

    return result;
}

// Merge des configurations
export const gabinConfig = deepMerge(gabinDefaultConfig, gabinLocalConfig);
export const companionConfig = deepMerge(companionDefaultConfig, companionLocalConfig);

// Export de la configuration app (inchangée)
export { appConfig, type AppConfig, type HttpConfig } from './app.config';
