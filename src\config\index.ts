/**
 * Export centralisé de toutes les configurations
 * Merge automatique des configurations par défaut + locales
 */

import { gabinDefaultConfig } from './defaults/gabin.default';
import { companionDefaultConfig } from './defaults/companion.default';
import type { GabinConfig } from '../gabin/gabin.types';
import type { CompanionConfig } from '../companion/companion.types';

// Import conditionnel des configurations locales avec gestion d'erreur propre
// Note: Utilisation de require() car les fichiers locaux peuvent ne pas exister
// et les imports ES6 dynamiques nécessiteraient une refactorisation complète
function loadGabinLocalConfig(): Partial<GabinConfig> {
    try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const localModule = require('./local/gabin.local') as { gabinLocalConfig?: Partial<GabinConfig> };
        return localModule.gabinLocalConfig || {};
    } catch {
        // Fichier local n'existe pas ou erreur d'import - retourner config vide
        return {};
    }
}

function loadCompanionLocalConfig(): Partial<CompanionConfig> {
    try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const localModule = require('./local/companion.local') as { companionLocalConfig?: Partial<CompanionConfig> };
        return localModule.companionLocalConfig || {};
    } catch {
        // Fichier local n'existe pas ou erreur d'import - retourner config vide
        return {};
    }
}

// Chargement des configurations locales
const gabinLocalConfig = loadGabinLocalConfig();
const companionLocalConfig = loadCompanionLocalConfig();

/**
 * Merge profond de deux objets de configuration
 */
function deepMerge<T>(defaults: T, overrides: Partial<T>): T {
    const result = { ...defaults };

    for (const key in overrides) {
        if (overrides[key] !== undefined) {
            if (typeof overrides[key] === 'object' && !Array.isArray(overrides[key]) && overrides[key] !== null) {
                result[key] = deepMerge(result[key] as any, overrides[key] as any);
            } else {
                result[key] = overrides[key] as any;
            }
        }
    }

    return result;
}

// Merge des configurations
export const gabinConfig = deepMerge(gabinDefaultConfig, gabinLocalConfig);
export const companionConfig = deepMerge(companionDefaultConfig, companionLocalConfig);

// Export de la configuration app (inchangée)
export { appConfig, type AppConfig, type HttpConfig } from './app.config';
