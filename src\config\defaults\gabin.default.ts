/**
 * ⚠️  CONFIGURATION PAR DÉFAUT GABIN - NE PAS MODIFIER
 * 
 * Ce fichier contient les valeurs par défaut pour Gabin et est versionné dans Git.
 * 
 * 🔧 Pour personnaliser la configuration :
 *    1. Les fichiers locaux sont auto-générés au premier démarrage
 *    2. Modifiez src/config/local/gabin.local.ts (ignoré par Git)
 *    3. Vos modifications locales surchargent ces valeurs par défaut
 * 
 * 🔄 Pour restaurer les valeurs par défaut :
 *    Supprimez src/config/local/gabin.local.ts et redémarrez l'application
 */

import { GabinConfig } from '../../gabin/gabin.types';

export const gabinDefaultConfig: GabinConfig = {
    network: {
        listenPort: 33123,
        sendPort: 32123,
        sendHost: '127.0.0.1',
    },
    timing: {
        pingInterval: 5000,
        pingTimeout: 3000,
        reconnectionDelay: 500,
    },
    registrations: [
        { type: 'autocam', path: '/autocam' },
        { type: 'micFeedback', path: '/mic/MIC1' },
        { type: 'micFeedback', path: '/mic/MIC2' },
    ],
};
