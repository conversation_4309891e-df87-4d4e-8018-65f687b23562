/**
 * 🔧 CONFIGURATION LOCALE GABIN
 * 
 * Ce fichier est ignoré par Git et contient votre configuration personnalisée.
 * Modifiez les valeurs ci-dessous selon vos besoins.
 * 
 * 💡 Conseils :
 *    - Décommentez seulement les sections que vous voulez personnaliser
 *    - Les valeurs non définies utilisent les defaults de gabin.default.ts
 *    - Ajoutez vos micros dans la section registrations
 * 
 * 🔄 Pour restaurer les valeurs par défaut :
 *    Supprimez ce fichier et redémarrez l'application
 */

import { GabinConfig } from '../../gabin/gabin.types';

export const gabinLocalConfig: Partial<GabinConfig> = {
    // 🌐 Configuration réseau
    // network: {
    //     listenPort: 33123,        // Port d'écoute pour les feedbacks de Gabin
    //     sendPort: 32123,         // Port d'envoi des commandes vers Gabin
    //     sendHost: '127.0.0.1',   // Adresse IP de Gabin
    // },

    // ⏱️ Configuration des timeouts
    // timing: {
    //     pingInterval: 5000,      // Intervalle de ping vers Gabin (ms)
    //     pingTimeout: 3000,       // Timeout du ping (ms)
    //     reconnectionDelay: 500,  // Délai avant re-registration (ms)
    // },

    // 🎤 Configuration des registrations (autocam + micros)
    // registrations: [
    //     { type: 'autocam', path: '/autocam' },
    //     { type: 'micFeedback', path: '/mic/MIC1' },
    //     { type: 'micFeedback', path: '/mic/MIC2' },
    //     // Ajoutez vos micros supplémentaires ici :
    //     // { type: 'micFeedback', path: '/mic/MIC3' },
    //     // { type: 'micFeedback', path: '/mic/MIC4' },
    // ],
};
