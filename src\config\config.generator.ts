import * as fs from 'fs';
import * as path from 'path';

/**
 * Générateur de fichiers de configuration locaux
 * Auto-génère les fichiers *.local.ts s'ils n'existent pas
 */
export class ConfigGenerator {
    private static readonly LOCAL_DIR = (() => {
        // Détecter si nous sommes en mode dev (src/) ou build (dist/)
        const currentDir = __dirname;
        if (currentDir.includes('dist')) {
            // Mode production : fichiers dans src/config/local depuis dist/config
            return path.join(__dirname, '..', '..', 'src', 'config', 'local');
        } else {
            // Mode développement : fichiers dans le même répertoire
            return path.join(__dirname, 'local');
        }
    })();

    /**
     * Génère tous les fichiers de configuration locaux manquants
     * Retourne true si des fichiers ont été générés (première fois)
     */
    static generateMissingLocalConfigs(): boolean {
        this.ensureLocalDirExists();

        const gabinGenerated = this.generateGabinLocalConfig();
        const companionGenerated = this.generateCompanionLocalConfig();

        const isFirstTime = gabinGenerated || companionGenerated;

        if (isFirstTime) {
            this.showFirstTimeMessage();
        }

        return isFirstTime;
    }

    /**
     * S'assure que le dossier local/ existe
     */
    private static ensureLocalDirExists(): void {
        if (!fs.existsSync(this.LOCAL_DIR)) {
            fs.mkdirSync(this.LOCAL_DIR, { recursive: true });
            console.log('[Config] Created local config directory');
        }
    }

    /**
     * Génère gabin.local.ts s'il n'existe pas
     * Retourne true si le fichier a été généré
     */
    private static generateGabinLocalConfig(): boolean {
        const filePath = path.join(this.LOCAL_DIR, 'gabin.local.ts');

        if (!fs.existsSync(filePath)) {
            const content = this.getGabinLocalTemplate();
            fs.writeFileSync(filePath, content, 'utf8');
            console.log('[Config] Generated gabin.local.ts - customize your Gabin configuration here');
            return true;
        }
        return false;
    }

    /**
     * Génère companion.local.ts s'il n'existe pas
     * Retourne true si le fichier a été généré
     */
    private static generateCompanionLocalConfig(): boolean {
        const filePath = path.join(this.LOCAL_DIR, 'companion.local.ts');

        if (!fs.existsSync(filePath)) {
            const content = this.getCompanionLocalTemplate();
            fs.writeFileSync(filePath, content, 'utf8');
            console.log('[Config] Generated companion.local.ts - customize your Companion configuration here');
            return true;
        }
        return false;
    }

    /**
     * Affiche le message de première configuration et arrête l'application
     */
    private static showFirstTimeMessage(): void {
        console.log('\n' + '='.repeat(80));
        console.log('🎉 CONFIGURATION INITIALE TERMINÉE');
        console.log('='.repeat(80));
        console.log('');
        console.log('📁 Les fichiers de configuration locaux ont été générés :');
        console.log('   • src/config/local/gabin.local.ts');
        console.log('   • src/config/local/companion.local.ts');
        console.log('');
        console.log('🔧 PROCHAINES ÉTAPES :');
        console.log('   1. Personnalisez vos configurations dans les fichiers locaux');
        console.log('   2. Ajoutez vos micros supplémentaires si nécessaire');
        console.log('   3. Redémarrez l\'application : npm run start');
        console.log('');
        console.log('💡 CONSEILS :');
        console.log('   • Les fichiers locaux sont ignorés par Git');
        console.log('   • Décommentez seulement les sections à personnaliser');
        console.log('   • Consultez le README.md pour plus d\'informations');
        console.log('');
        console.log('='.repeat(80));
        console.log('');

        // Arrêter l'application après un court délai
        setTimeout(() => {
            console.log('🛑 Application arrêtée pour configuration. Redémarrez après personnalisation.');
            process.exit(0);
        }, 1000);
    }

    /**
     * Template pour gabin.local.ts
     */
    private static getGabinLocalTemplate(): string {
        return `/**
 * 🔧 CONFIGURATION LOCALE GABIN
 * 
 * Ce fichier est ignoré par Git et contient votre configuration personnalisée.
 * Modifiez les valeurs ci-dessous selon vos besoins.
 * 
 * 💡 Conseils :
 *    - Décommentez seulement les sections que vous voulez personnaliser
 *    - Les valeurs non définies utilisent les defaults de gabin.default.ts
 *    - Ajoutez vos micros dans la section registrations
 * 
 * 🔄 Pour restaurer les valeurs par défaut :
 *    Supprimez ce fichier et redémarrez l'application
 */

import { GabinConfig } from '../../gabin/gabin.types';

export const gabinLocalConfig: Partial<GabinConfig> = {
    // 🌐 Configuration réseau
    // network: {
    //     listenPort: 33123,        // Port d'écoute pour les feedbacks de Gabin
    //     sendPort: 32123,         // Port d'envoi des commandes vers Gabin
    //     sendHost: '127.0.0.1',   // Adresse IP de Gabin
    // },

    // ⏱️ Configuration des timeouts
    // timing: {
    //     pingInterval: 5000,      // Intervalle de ping vers Gabin (ms)
    //     pingTimeout: 3000,       // Timeout du ping (ms)
    //     reconnectionDelay: 500,  // Délai avant re-registration (ms)
    // },

    // 🎤 Configuration des registrations (autocam + micros)
    // registrations: [
    //     { type: 'autocam', path: '/autocam' },
    //     { type: 'micFeedback', path: '/mic/MIC1' },
    //     { type: 'micFeedback', path: '/mic/MIC2' },
    //     // Ajoutez vos micros supplémentaires ici :
    //     // { type: 'micFeedback', path: '/mic/MIC3' },
    //     // { type: 'micFeedback', path: '/mic/MIC4' },
    // ],
};
`;
    }

    /**
     * Template pour companion.local.ts
     */
    private static getCompanionLocalTemplate(): string {
        return `/**
 * 🔧 CONFIGURATION LOCALE COMPANION
 * 
 * Ce fichier est ignoré par Git et contient votre configuration personnalisée.
 * Modifiez les valeurs ci-dessous selon vos besoins.
 * 
 * 💡 Conseils :
 *    - Décommentez seulement les sections que vous voulez personnaliser
 *    - Les valeurs non définies utilisent les defaults de companion.default.ts
 *    - Ajoutez vos feedbacks dans la section feedbacks
 * 
 * 🔄 Pour restaurer les valeurs par défaut :
 *    Supprimez ce fichier et redémarrez l'application
 */

import { CompanionConfig } from '../../companion/companion.types';

export const companionLocalConfig: Partial<CompanionConfig> = {
    // 🌐 Configuration réseau
    // network: {
    //     listenPort: 33223,       // Port d'écoute pour les actions de Companion
    //     host: '127.0.0.1',       // Adresse IP de Companion
    //     port: 33224,             // Port de Companion pour les feedbacks
    // },

    // ⏱️ Configuration des timeouts
    // timing: {
    //     connectionCheckInterval: 6000,  // Intervalle de vérification Gabin (ms)
    //     initialStateDelay: 1000,        // Délai avant envoi états initiaux (ms)
    // },

    // 🎛️ Configuration des feedbacks (autocam + micros)
    // feedbacks: [
    //     // Autocam
    //     {
    //         type: 'autocam',
    //         path: '/autocam',
    //     },
    //     // Micros
    //     {
    //         type: 'mic',
    //         path: '/mic/MIC1',
    //         micName: 'MIC1',
    //     },
    //     {
    //         type: 'mic',
    //         path: '/mic/MIC2',
    //         micName: 'MIC2',
    //     },
    //     // Ajoutez vos micros supplémentaires ici :
    //     // {
    //     //     type: 'mic',
    //     //     path: '/mic/MIC3',
    //     //     micName: 'MIC3',
    //     // },
    // ],
};
`;
    }
}
