import { Injectable, OnModuleInit } from '@nestjs/common';
import { GabinService } from './gabin.service';
import { HubService } from '../core/hub.service';
import { Server, Client, Message } from 'node-osc';

@Injectable()
export class GabinTransportService implements OnModuleInit {
    private readonly listenPort = 33123;
    private readonly sendPort = 32123;
    private readonly sendHost = '127.0.0.1';

    private readonly registerTypes = [
        { type: 'autocam', path: '/autocam' },
        { type: 'micFeedback', path: '/mic/MIC1' },
        { type: 'micFeedback', path: '/mic/MIC2' },
    ];

    constructor(
        private readonly gabinService: GabinService,
        private readonly hubService: HubService,
    ) {}

    onModuleInit() {
        this.startOscServer();
        this.setupCommandListeners();
        this.setupReconnectionListener();
        this.startGabinPing();
    }

    private startOscServer() {
        const server = new Server(this.listenPort, '0.0.0.0', () => {
            console.log(`[Gabin] OSC server listening on port ${this.listenPort}`);
            this.registerAll();
        });

        server.on('message', (msg) => {
            const [path, value] = msg;

            if (path === '/autocam') {
                const bool = value === 'true' || value === 1;
                this.hubService.setAutocam(bool);
            } else if (path.startsWith('/mic/')) {
                const mic = path.split('/')[2];
                const bool = value === 'true' || value === 1;
                this.hubService.setMic(mic, bool);
            } else if (path === '/gabin/is-ready') {
                // Réponse à notre ping - Gabin est connecté
                const isReady = value === 'true' || value === 1 || value === true;
                // Le log sera affiché par setServiceStatus() seulement si l'état change
                this.hubService.setServiceStatus('gabin', { connected: isReady });
            } else {
                console.log(`[gabin] Unhandled path: ${path}`, value);
            }
        });
    }

    private setupCommandListeners() {
        // Écouter les commandes autocam depuis le service
        this.gabinService.onAutocamCommand((value: boolean) => {
            this.sendAutocamCommand(value);
        });

        // Écouter les commandes micro depuis le service
        this.gabinService.onMicCommand((name: string, value: boolean) => {
            this.sendMicCommand(name, value);
        });
    }

    private setupReconnectionListener() {
        // Écouter les changements de statut de Gabin via HubService
        this.hubService.onEvent((event) => {
            if (event.type === 'service_status_changed' && event.data.serviceName === 'gabin' && event.data.status?.connected === true) {
                console.log('[gabin] Gabin reconnected - re-registering OSC feedbacks');
                // Petit délai pour laisser Gabin se stabiliser
                setTimeout(() => {
                    this.registerAll();
                }, 500);
            }
        });
    }

    sendRegister(type: string, path: string) {
        const client = new Client(this.sendHost, this.sendPort);
        const msg = new Message(`/register/${type}`, '127.0.0.1', this.listenPort, path);
        client.send(msg, (err) => {
            if (err) console.error(`[gabin] Failed to register ${type}`, err);
            else console.log(`[gabin] Sent register for ${type} (${path})`);
            client.close();
        });
    }

    registerAll() {
        for (const entry of this.registerTypes) {
            this.sendRegister(entry.type, entry.path);
        }
    }

    /**
     * Envoie une commande autocam vers Gabin
     */
    sendAutocamCommand(value: boolean) {
        const client = new Client(this.sendHost, this.sendPort);
        const msg = new Message('/autocam', value ? 1 : 0);
        client.send(msg, (err) => {
            if (err) {
                console.error(`[gabin] Failed to send autocam command`, err);
            } else {
                console.log(`[gabin] Sent autocam command: ${value}`);
            }
            client.close();
        });
    }

    /**
     * Envoie une commande micro vers Gabin
     */
    sendMicCommand(micName: string, value: boolean) {
        const client = new Client(this.sendHost, this.sendPort);
        const msg = new Message(`/mic/${micName}`, value ? 1 : 0);
        client.send(msg, (err) => {
            if (err) {
                console.error(`[gabin] Failed to send mic command for ${micName}`, err);
            } else {
                console.log(`[gabin] Sent mic command for ${micName}: ${value}`);
            }
            client.close();
        });
    }

    /**
     * Démarre le système de ping pour vérifier l'état de Gabin
     */
    private startGabinPing() {
        console.log('[gabin] Starting Gabin connection monitoring...');

        // Ping immédiat
        this.pingGabin();

        // Ping toutes les 5 secondes
        setInterval(() => {
            this.pingGabin();
        }, 5000);
    }

    /**
     * Envoie un ping vers Gabin pour vérifier sa connexion
     */
    private pingGabin() {
        const client = new Client(this.sendHost, this.sendPort);
        const msg = new Message('/gabin/is-ready', '127.0.0.1', this.listenPort, '/gabin/is-ready');

        // Timeout si Gabin ne répond pas dans les 3 secondes
        const timeoutId = setTimeout(() => {
            // Le log sera affiché par setServiceStatus() seulement si l'état change
            this.hubService.setServiceStatus('gabin', { connected: false });
        }, 3000);

        // Stocker le timeout pour pouvoir l'annuler si on reçoit une réponse
        this.gabinService.setPingTimeout(timeoutId);

        client.send(msg, (err) => {
            if (err) {
                console.error(`[gabin] Failed to send ping to Gabin:`, err);
                // Si l'envoi échoue, considérer Gabin comme déconnecté
                clearTimeout(timeoutId);
                this.hubService.setServiceStatus('gabin', { connected: false });
            }
            // Suppression du log "Sent ping to Gabin" - trop verbeux
            client.close();
        });
    }
}
