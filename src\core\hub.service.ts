import { Injectable } from '@nestjs/common';

/**
 * Interface pour les événements du Hub
 */
export interface HubEvent {
    type: 'autocam_changed' | 'mic_changed' | 'service_status_changed';
    data: {
        value?: boolean;
        name?: string;
        serviceName?: string;
        status?: ServiceStatus;
    };
}

/**
 * Interface pour le statut des services
 */
export interface ServiceStatus {
    connected: boolean;
    lastSeen?: Date;
}

/**
 * Interface pour l'état global du Hub
 */
export interface HubState {
    // États des fonctionnalités
    autocam: boolean | null;
    mics: Record<string, boolean>;

    // États des services
    services: {
        gabin: ServiceStatus;
        companion: ServiceStatus;
    };
}

/**
 * Service central qui gère l'état global et les communications entre services
 */
@Injectable()
export class HubService {
    private state: HubState = {
        autocam: null,
        mics: {},
        services: {
            gabin: { connected: false },
            companion: { connected: false },
        },
    };

    // Système d'événements simple
    private eventListeners: Array<(event: HubEvent) => void> = [];

    /**
     * S'abonner aux événements du Hub
     */
    onEvent(callback: (event: HubEvent) => void) {
        this.eventListeners.push(callback);
    }

    /**
     * Émettre un événement
     */
    private emitEvent(event: HubEvent) {
        this.eventListeners.forEach((callback) => {
            try {
                callback(event);
            } catch (error) {
                console.error('[Hub] Error in event callback:', error);
            }
        });
    }

    // === GESTION DE L'AUTOCAM ===

    /**
     * Met à jour l'état de l'autocam
     */
    setAutocam(value: boolean) {
        if (this.state.autocam !== value) {
            this.state.autocam = value;
            console.log(`[Hub] Autocam changed to: ${value}`);

            this.emitEvent({
                type: 'autocam_changed',
                data: { value },
            });
        }
    }

    /**
     * Récupère l'état de l'autocam
     */
    getAutocam(): boolean | null {
        return this.state.autocam;
    }

    // === GESTION DES MICROS ===

    /**
     * Met à jour l'état d'un micro
     */
    setMic(name: string, value: boolean) {
        if (this.state.mics[name] !== value) {
            this.state.mics[name] = value;
            console.log(`[Hub] Mic '${name}' changed to: ${value}`);

            this.emitEvent({
                type: 'mic_changed',
                data: { name, value },
            });
        }
    }

    /**
     * Récupère l'état d'un micro
     */
    getMic(name: string): boolean | undefined {
        return this.state.mics[name];
    }

    /**
     * Récupère tous les états des micros
     */
    getAllMics(): Record<string, boolean> {
        return { ...this.state.mics };
    }

    // === GESTION DES SERVICES ===

    // Gestion des timeouts pour les pings
    private serviceTimeouts: Record<string, NodeJS.Timeout> = {};

    /**
     * Met à jour le statut d'un service
     */
    setServiceStatus(serviceName: 'gabin' | 'companion', status: Partial<ServiceStatus>) {
        // Annuler le timeout si on reçoit une réponse
        if (status.connected === true && this.serviceTimeouts[serviceName]) {
            clearTimeout(this.serviceTimeouts[serviceName]);
            delete this.serviceTimeouts[serviceName];
        }

        const currentStatus = this.state.services[serviceName];
        const newStatus = { ...currentStatus, ...status, lastSeen: new Date() };

        if (currentStatus.connected !== newStatus.connected) {
            console.log(`[Hub] Service '${serviceName}' status: ${newStatus.connected ? 'CONNECTED' : 'DISCONNECTED'}`);
            this.state.services[serviceName] = newStatus;

            this.emitEvent({
                type: 'service_status_changed',
                data: { serviceName, status: newStatus },
            });
        } else {
            this.state.services[serviceName] = newStatus;
        }
    }

    /**
     * Stocke le timeout d'un service
     */
    setServiceTimeout(serviceName: 'gabin' | 'companion', timeoutId: NodeJS.Timeout) {
        // Annuler l'ancien timeout s'il existe
        if (this.serviceTimeouts[serviceName]) {
            clearTimeout(this.serviceTimeouts[serviceName]);
        }
        this.serviceTimeouts[serviceName] = timeoutId;
    }

    /**
     * Récupère le statut d'un service
     */
    getServiceStatus(serviceName: 'gabin' | 'companion'): ServiceStatus {
        return { ...this.state.services[serviceName] };
    }

    /**
     * Vérifie si un service est connecté
     */
    isServiceConnected(serviceName: 'gabin' | 'companion'): boolean {
        return this.state.services[serviceName].connected;
    }

    // === STATUS JSON ===

    /**
     * Récupère l'état complet pour l'endpoint /status
     */
    getStatus(): HubState {
        return {
            autocam: this.state.autocam,
            mics: { ...this.state.mics },
            services: {
                gabin: { ...this.state.services.gabin },
                companion: { ...this.state.services.companion },
            },
        };
    }
}
