import { GabinConfig } from '../gabin/gabin.types';

/**
 * Configuration pour le module Gabin
 */
export const gabinConfig: GabinConfig = {
    network: {
        listenPort: process.env.GABIN_LISTEN_PORT ? parseInt(process.env.GABIN_LISTEN_PORT, 10) : 33123,
        sendPort: process.env.GABIN_SEND_PORT ? parseInt(process.env.GABIN_SEND_PORT, 10) : 32123,
        sendHost: process.env.GABIN_HOST || '127.0.0.1',
    },
    timing: {
        pingInterval: process.env.GABIN_PING_INTERVAL ? parseInt(process.env.GABIN_PING_INTERVAL, 10) : 5000,
        pingTimeout: process.env.GABIN_PING_TIMEOUT ? parseInt(process.env.GABIN_PING_TIMEOUT, 10) : 3000,
        reconnectionDelay: process.env.GABIN_RECONNECTION_DELAY ? parseInt(process.env.GABIN_RECONNECTION_DELAY, 10) : 500,
    },
    registrations: [
        { type: 'autocam', path: '/autocam' },
        { type: 'micFeedback', path: '/mic/MIC1' },
        { type: 'micFeedback', path: '/mic/MIC2' },
        // Ajouter d'autres micros ici selon vos besoins
    ],
};
