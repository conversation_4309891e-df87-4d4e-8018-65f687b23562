/**
 * 🔧 CONFIGURATION LOCALE COMPANION
 * 
 * Ce fichier est ignoré par Git et contient votre configuration personnalisée.
 * Modifiez les valeurs ci-dessous selon vos besoins.
 * 
 * 💡 Conseils :
 *    - Décommentez seulement les sections que vous voulez personnaliser
 *    - Les valeurs non définies utilisent les defaults de companion.default.ts
 *    - Ajoutez vos feedbacks dans la section feedbacks
 * 
 * 🔄 Pour restaurer les valeurs par défaut :
 *    Supprimez ce fichier et redémarrez l'application
 */

import { CompanionConfig } from '../../companion/companion.types';

export const companionLocalConfig: Partial<CompanionConfig> = {
    // 🌐 Configuration réseau
    // network: {
    //     listenPort: 33223,       // Port d'écoute pour les actions de Companion
    //     host: '127.0.0.1',       // Adresse IP de Companion
    //     port: 33224,             // Port de Companion pour les feedbacks
    // },

    // ⏱️ Configuration des timeouts
    // timing: {
    //     connectionCheckInterval: 6000,  // Intervalle de vérification Gabin (ms)
    //     initialStateDelay: 1000,        // Délai avant envoi états initiaux (ms)
    // },

    // 🎛️ Configuration des feedbacks (autocam + micros)
    // feedbacks: [
    //     // Autocam
    //     {
    //         type: 'autocam',
    //         path: '/autocam',
    //     },
    //     // Micros
    //     {
    //         type: 'mic',
    //         path: '/mic/MIC1',
    //         micName: 'MIC1',
    //     },
    //     {
    //         type: 'mic',
    //         path: '/mic/MIC2',
    //         micName: 'MIC2',
    //     },
    //     // Ajoutez vos micros supplémentaires ici :
    //     // {
    //     //     type: 'mic',
    //     //     path: '/mic/MIC3',
    //     //     micName: 'MIC3',
    //     // },
    // ],
};
