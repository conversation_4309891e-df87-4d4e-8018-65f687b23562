import { Injectable } from '@nestjs/common';
import { Client, Message } from 'node-osc';

@Injectable()
export class CompanionService {
    private readonly companionHost = '127.0.0.1';
    private readonly companionPort = 32500;

    sendAutocamStatus(isOn: boolean) {
        const client = new Client(this.companionHost, this.companionPort);
        const msg = new Message('/autocam', isOn ? 1 : 0);
        client.send(msg, () => client.close());
    }

    sendMicStatus(name: string, isOn: boolean) {
        const client = new Client(this.companionHost, this.companionPort);
        const msg = new Message(`/mic/${name}`, isOn ? 1 : 0);
        client.send(msg, () => client.close());
    }
}
